"""
恒生指数成分股每日毛利率数据获取器

该脚本用于获取恒生指数成分股的毛利率数据，并将季度财务数据插值为每日数据。
毛利率 = (营业收入 - 营业成本) / 营业收入

主要功能：
1. 从东方财富API获取港股财务数据（利润表）
2. 计算毛利率指标
3. 将季度数据插值为每日数据
4. 缓存数据以提高性能
"""

import os
import sys
import pandas as pd
import numpy as np
import requests
import json
import time
import pickle
from datetime import datetime, timedelta
from tqdm import tqdm
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("hsi_gross_margin_download.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class HSIGrossMarginDownloader:
    def __init__(self):
        """初始化下载器"""
        self.output_dir = "data/hsi_gross_margin_data"
        self.cache_dir = "data/hsi_gross_margin_cache"
        self.constituents_file = "data_files/hsi_constituents.csv"
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.cache_dir, exist_ok=True)
        
        self.hsi_constituents = []
        self.financial_data = {}
        
    def load_hsi_constituents(self) -> bool:
        """加载恒生指数成分股列表"""
        try:
            logger.info(f"📁 从 {self.constituents_file} 加载恒生指数成分股...")
            
            df = pd.read_csv(self.constituents_file, dtype={'代码': str})
            
            # 格式化股票代码
            hsi_stocks = []
            for _, row in df.iterrows():
                code = str(row['代码']).strip()
                name = str(row['名称']).strip()
                if code and code != 'nan':
                    code_formatted = str(code).zfill(5)  # 补齐到5位数
                    hsi_stocks.append({
                        'code': code_formatted,
                        'name': name
                    })
            
            self.hsi_constituents = hsi_stocks
            logger.info(f"✅ 已加载 {len(hsi_stocks)} 只恒生指数成分股")
            return True
            
        except Exception as e:
            logger.error(f"❌ 加载成分股列表失败: {e}")
            return False
    
    def format_hk_stock_code(self, code):
        """格式化港股代码，用于API请求"""
        # 确保代码是字符串并补齐前导零
        code_str = str(code).zfill(5)
        # 对于港股，使用5位数字格式加.HK后缀
        return f"{code_str}.HK"
    
    def get_cache_file_path(self, stock_code, data_type="financial"):
        """获取缓存文件路径"""
        return os.path.join(self.cache_dir, f"{stock_code}_{data_type}_data.pkl")
    
    def is_cache_valid(self, stock_code, max_age_days=7):
        """检查缓存是否有效（7天内的数据认为有效）"""
        cache_file = self.get_cache_file_path(stock_code)
        if not os.path.exists(cache_file):
            return False
        
        # 检查文件修改时间
        file_time = datetime.fromtimestamp(os.path.getmtime(cache_file))
        age = datetime.now() - file_time
        return age.days < max_age_days
    
    def load_cached_data(self, stock_code):
        """从缓存加载数据"""
        cache_file = self.get_cache_file_path(stock_code)
        try:
            with open(cache_file, 'rb') as f:
                return pickle.load(f)
        except Exception as e:
            logger.warning(f"⚠️  加载 {stock_code} 缓存失败: {e}")
            return None
    
    def save_to_cache(self, stock_code, data):
        """保存数据到缓存"""
        cache_file = self.get_cache_file_path(stock_code)
        try:
            with open(cache_file, 'wb') as f:
                pickle.dump(data, f)
        except Exception as e:
            logger.warning(f"⚠️  保存 {stock_code} 缓存失败: {e}")

    def download_financial_data(self, stock_code, stock_name):
        """下载单只股票的财务数据（利润表）"""
        try:
            # 格式化股票代码
            formatted_code = self.format_hk_stock_code(stock_code)
            logger.info(f"📊 下载 {formatted_code} ({stock_name}) 的财务数据")

            # 构建API URL - 使用港股利润表接口
            url = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_INCOME_PC&columns=ALL&quoteColumns=&filter=(SECUCODE%3D%22{formatted_code}%22)&pageNumber=1&pageSize=50&sortTypes=-1&sortColumns=REPORT_DATE&source=F10&client=PC"

            # 发送请求
            response = requests.get(url, timeout=30)

            if response.status_code != 200:
                logger.warning(f"⚠️  请求失败: {formatted_code}, 状态码: {response.status_code}")
                return None

            # 解析JSON响应
            data = response.json()

            if 'result' not in data or 'data' not in data['result'] or not data['result']['data']:
                logger.warning(f"⚠️  未找到 {formatted_code} 的财务数据")
                return None

            # 提取财务数据
            financial_data = data['result']['data']
            logger.info(f"✅ 获取到 {formatted_code} 的 {len(financial_data)} 条财务记录")

            # 转换为DataFrame
            df = pd.DataFrame(financial_data)

            # 处理数据：计算毛利率
            processed_df = self.process_income_statement_data(df, stock_code, stock_name)

            if processed_df is not None:
                # 保存到缓存
                self.save_to_cache(stock_code, processed_df)

                # 保存原始数据到文件
                output_file = os.path.join(self.output_dir, f"{stock_code}_gross_margin_data.csv")
                processed_df.to_csv(output_file, index=False, encoding='utf-8-sig')
                logger.info(f"💾 已保存 {formatted_code} 的毛利率数据到 {output_file}")
                return processed_df
            else:
                logger.warning(f"⚠️  处理 {formatted_code} 的财务数据失败")
                return None

        except Exception as e:
            logger.error(f"❌ 下载 {stock_code} 的财务数据时出错: {str(e)}")
            return None

    def process_income_statement_data(self, df, stock_code, stock_name):
        """处理利润表数据，计算毛利率"""
        try:
            # 确保必要的列存在
            required_columns = ['REPORT_DATE', 'STD_ITEM_CODE', 'STD_ITEM_NAME', 'AMOUNT']
            for col in required_columns:
                if col not in df.columns:
                    logger.warning(f"⚠️  {stock_code} 的数据缺少必要的列: {col}")
                    return None

            # 转换日期列
            df['REPORT_DATE'] = pd.to_datetime(df['REPORT_DATE'])

            # 获取所有唯一的报告日期
            report_dates = sorted(df['REPORT_DATE'].unique())
            result_rows = []

            # 对每个报告日期处理数据
            for date in report_dates:
                row = {
                    'stock_code': stock_code,
                    'stock_name': stock_name,
                    'report_date': date
                }

                # 提取该日期的数据
                date_df = df[df['REPORT_DATE'] == date]

                # 查找营业收入
                revenue_items = date_df[date_df['STD_ITEM_NAME'].str.contains('营业收入|经营收入|总收入', na=False)]
                if not revenue_items.empty:
                    # 优先使用"营业收入"
                    for _, item in revenue_items.iterrows():
                        if '营业收入' in item['STD_ITEM_NAME'] and '其他' not in item['STD_ITEM_NAME']:
                            row['revenue'] = item['AMOUNT']
                            row['revenue_item_name'] = item['STD_ITEM_NAME']
                            break
                    # 如果没有找到营业收入，使用第一个收入项目
                    if 'revenue' not in row and not revenue_items.empty:
                        row['revenue'] = revenue_items.iloc[0]['AMOUNT']
                        row['revenue_item_name'] = revenue_items.iloc[0]['STD_ITEM_NAME']

                # 查找营业成本
                cost_items = date_df[date_df['STD_ITEM_NAME'].str.contains('营业成本|经营成本|销售成本', na=False)]
                if not cost_items.empty:
                    # 优先使用"营业成本"
                    for _, item in cost_items.iterrows():
                        if '营业成本' in item['STD_ITEM_NAME']:
                            row['cost_of_revenue'] = item['AMOUNT']
                            row['cost_item_name'] = item['STD_ITEM_NAME']
                            break
                    # 如果没有找到营业成本，使用第一个成本项目
                    if 'cost_of_revenue' not in row and not cost_items.empty:
                        row['cost_of_revenue'] = cost_items.iloc[0]['AMOUNT']
                        row['cost_item_name'] = cost_items.iloc[0]['STD_ITEM_NAME']

                # 计算毛利率
                if 'revenue' in row and 'cost_of_revenue' in row:
                    try:
                        revenue = float(row['revenue']) if row['revenue'] else 0
                        cost = float(row['cost_of_revenue']) if row['cost_of_revenue'] else 0

                        if revenue > 0:
                            gross_margin = (revenue - cost) / revenue
                            row['gross_margin'] = gross_margin
                            row['gross_profit'] = revenue - cost
                        else:
                            row['gross_margin'] = None
                            row['gross_profit'] = None
                    except (ValueError, TypeError):
                        row['gross_margin'] = None
                        row['gross_profit'] = None

                # 只有当找到了收入数据时才添加这一行
                if 'revenue' in row:
                    result_rows.append(row)

            # 创建结果DataFrame
            if not result_rows:
                logger.warning(f"⚠️  {stock_code} 没有找到收入数据")
                return None

            result_df = pd.DataFrame(result_rows)

            # 添加年份和季度信息
            result_df['year'] = result_df['report_date'].dt.year
            result_df['month'] = result_df['report_date'].dt.month
            result_df['quarter'] = result_df['month'].apply(lambda x: (x-1)//3 + 1)
            result_df['year_quarter'] = result_df['year'].astype(str) + '-Q' + result_df['quarter'].astype(str)

            # 确保数值列是数值类型
            numeric_columns = ['revenue', 'cost_of_revenue', 'gross_margin', 'gross_profit']
            for col in numeric_columns:
                if col in result_df.columns:
                    result_df[col] = pd.to_numeric(result_df[col], errors='coerce')

            # 按日期排序
            result_df = result_df.sort_values('report_date').reset_index(drop=True)

            return result_df

        except Exception as e:
            logger.error(f"❌ 处理 {stock_code} 的财务数据时出错: {str(e)}")
            return None

    def interpolate_to_daily(self, quarterly_data, stock_code, start_date=None, end_date=None):
        """将季度毛利率数据插值为每日数据"""
        try:
            if quarterly_data is None or quarterly_data.empty:
                logger.warning(f"⚠️  {stock_code} 没有季度数据可供插值")
                return None

            # 设置默认日期范围（最近5年）
            if end_date is None:
                end_date = datetime.now().date()
            if start_date is None:
                start_date = end_date - timedelta(days=1825)  # 5年

            # 创建日期范围
            date_range = pd.date_range(start=start_date, end=end_date, freq='D')

            # 准备插值数据
            quarterly_data = quarterly_data.copy()
            quarterly_data['report_date'] = pd.to_datetime(quarterly_data['report_date']).dt.date
            quarterly_data = quarterly_data.sort_values('report_date')

            # 创建每日数据框架
            daily_data = pd.DataFrame({
                'date': date_range.date,
                'stock_code': stock_code,
                'stock_name': quarterly_data['stock_name'].iloc[0] if not quarterly_data.empty else ''
            })

            # 对毛利率进行插值
            if 'gross_margin' in quarterly_data.columns:
                # 创建插值函数
                valid_data = quarterly_data.dropna(subset=['gross_margin'])
                if len(valid_data) >= 2:
                    # 使用线性插值
                    from scipy.interpolate import interp1d

                    # 转换日期为数值
                    x_dates = [d.toordinal() for d in valid_data['report_date']]
                    y_values = valid_data['gross_margin'].values

                    # 创建插值函数
                    f = interp1d(x_dates, y_values, kind='linear',
                               bounds_error=False, fill_value='extrapolate')

                    # 对每日数据进行插值
                    daily_ordinals = [d.toordinal() for d in daily_data['date']]
                    daily_data['gross_margin'] = f(daily_ordinals)

                    # 限制毛利率在合理范围内（-100%到100%）
                    daily_data['gross_margin'] = daily_data['gross_margin'].clip(-1.0, 1.0)

                elif len(valid_data) == 1:
                    # 只有一个数据点，使用常数值
                    daily_data['gross_margin'] = valid_data['gross_margin'].iloc[0]
                else:
                    # 没有有效数据
                    daily_data['gross_margin'] = np.nan
            else:
                daily_data['gross_margin'] = np.nan

            # 添加数据来源标记
            daily_data['data_source'] = 'interpolated'
            daily_data['last_update'] = datetime.now()

            return daily_data

        except Exception as e:
            logger.error(f"❌ 插值 {stock_code} 数据时出错: {str(e)}")
            return None

    def download_all_stocks(self, max_workers=3, use_cache=True):
        """下载所有恒生指数成分股的毛利率数据"""
        if not self.hsi_constituents:
            logger.error("❌ 请先加载恒生指数成分股列表")
            return False

        logger.info(f"🚀 开始下载 {len(self.hsi_constituents)} 只恒生指数成分股的毛利率数据")

        success_count = 0
        cached_count = 0
        failed_stocks = []

        # 使用ThreadPoolExecutor进行并行处理
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 创建任务
            future_to_stock = {}

            for stock in self.hsi_constituents:
                stock_code = stock['code']
                stock_name = stock['name']

                # 检查缓存
                if use_cache and self.is_cache_valid(stock_code):
                    cached_data = self.load_cached_data(stock_code)
                    if cached_data is not None:
                        self.financial_data[stock_code] = cached_data
                        success_count += 1
                        cached_count += 1
                        logger.info(f"📁 {stock_code} ({stock_name}) 使用缓存数据")
                        continue

                # 提交下载任务
                future = executor.submit(self.download_financial_data, stock_code, stock_name)
                future_to_stock[future] = (stock_code, stock_name)

            # 处理完成的任务
            for future in tqdm(as_completed(future_to_stock),
                             total=len(future_to_stock),
                             desc="下载财务数据"):
                stock_code, stock_name = future_to_stock[future]

                try:
                    result = future.result()
                    if result is not None:
                        self.financial_data[stock_code] = result
                        success_count += 1
                    else:
                        failed_stocks.append((stock_code, stock_name))
                except Exception as e:
                    logger.error(f"❌ 处理 {stock_code} ({stock_name}) 时出错: {str(e)}")
                    failed_stocks.append((stock_code, stock_name))

                # 添加延迟避免API限制
                time.sleep(0.5)

        # 报告结果
        logger.info(f"📊 下载完成统计:")
        logger.info(f"  ✅ 成功: {success_count}/{len(self.hsi_constituents)}")
        logger.info(f"  📁 缓存: {cached_count}")
        logger.info(f"  ❌ 失败: {len(failed_stocks)}")

        if failed_stocks:
            logger.warning("❌ 下载失败的股票:")
            for code, name in failed_stocks:
                logger.warning(f"  - {code} ({name})")

        return success_count > 0

    def generate_daily_data(self, start_date=None, end_date=None):
        """生成所有股票的每日毛利率数据"""
        if not self.financial_data:
            logger.error("❌ 没有财务数据可供处理，请先下载数据")
            return None

        logger.info("📈 开始生成每日毛利率数据...")

        all_daily_data = []

        for stock_code, quarterly_data in self.financial_data.items():
            stock_name = quarterly_data['stock_name'].iloc[0] if not quarterly_data.empty else ''
            logger.info(f"📊 处理 {stock_code} ({stock_name}) 的数据插值...")

            # 插值为每日数据
            daily_data = self.interpolate_to_daily(quarterly_data, stock_code, start_date, end_date)

            if daily_data is not None:
                all_daily_data.append(daily_data)

                # 保存单个股票的每日数据
                output_file = os.path.join(self.output_dir, f"{stock_code}_daily_gross_margin.csv")
                daily_data.to_csv(output_file, index=False, encoding='utf-8-sig')
                logger.info(f"💾 已保存 {stock_code} 的每日毛利率数据")

        if all_daily_data:
            # 合并所有数据
            combined_data = pd.concat(all_daily_data, ignore_index=True)

            # 保存合并数据
            combined_file = os.path.join(self.output_dir, "hsi_all_daily_gross_margin.csv")
            combined_data.to_csv(combined_file, index=False, encoding='utf-8-sig')
            logger.info(f"💾 已保存所有股票的每日毛利率数据到 {combined_file}")

            # 生成数据统计报告
            self.generate_summary_report(combined_data)

            return combined_data
        else:
            logger.error("❌ 没有生成任何每日数据")
            return None

    def generate_summary_report(self, daily_data):
        """生成数据统计报告"""
        try:
            logger.info("📋 生成数据统计报告...")

            # 基本统计
            total_records = len(daily_data)
            unique_stocks = daily_data['stock_code'].nunique()
            date_range = f"{daily_data['date'].min()} 到 {daily_data['date'].max()}"

            # 毛利率统计
            valid_margin_data = daily_data.dropna(subset=['gross_margin'])
            avg_gross_margin = valid_margin_data['gross_margin'].mean() if not valid_margin_data.empty else 0

            # 按股票统计
            stock_stats = daily_data.groupby(['stock_code', 'stock_name']).agg({
                'gross_margin': ['count', 'mean', 'std', 'min', 'max']
            }).round(4)

            # 保存统计报告
            report_file = os.path.join(self.output_dir, "gross_margin_summary_report.txt")
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("恒生指数成分股每日毛利率数据统计报告\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"数据日期范围: {date_range}\n")
                f.write(f"总记录数: {total_records:,}\n")
                f.write(f"股票数量: {unique_stocks}\n")
                f.write(f"平均毛利率: {avg_gross_margin:.2%}\n\n")

                f.write("各股票毛利率统计:\n")
                f.write("-" * 30 + "\n")
                for (code, name), stats in stock_stats.iterrows():
                    f.write(f"{code} ({name}):\n")
                    f.write(f"  数据点数: {int(stats[('gross_margin', 'count')])}\n")
                    f.write(f"  平均毛利率: {stats[('gross_margin', 'mean')]:.2%}\n")
                    f.write(f"  标准差: {stats[('gross_margin', 'std')]:.4f}\n")
                    f.write(f"  最小值: {stats[('gross_margin', 'min')]:.2%}\n")
                    f.write(f"  最大值: {stats[('gross_margin', 'max')]:.2%}\n\n")

            logger.info(f"📋 统计报告已保存到 {report_file}")

            # 打印简要统计
            logger.info("📊 数据统计摘要:")
            logger.info(f"  📅 日期范围: {date_range}")
            logger.info(f"  📈 总记录数: {total_records:,}")
            logger.info(f"  🏢 股票数量: {unique_stocks}")
            logger.info(f"  📊 平均毛利率: {avg_gross_margin:.2%}")

        except Exception as e:
            logger.error(f"❌ 生成统计报告时出错: {str(e)}")

def main():
    """主函数"""
    start_time = time.time()

    logger.info("🚀 恒生指数成分股每日毛利率数据获取器启动")
    logger.info("📝 注意：毛利率数据基于季度财务报表，将插值为每日数据")

    downloader = HSIGrossMarginDownloader()

    # 1. 加载成分股列表
    logger.info("📋 步骤1: 加载恒生指数成分股列表")
    if not downloader.load_hsi_constituents():
        logger.error("❌ 无法加载恒生指数成分股列表，程序退出")
        return

    # 2. 下载财务数据
    logger.info("📊 步骤2: 下载财务数据")
    if not downloader.download_all_stocks(max_workers=3, use_cache=True):
        logger.error("❌ 财务数据下载失败，程序退出")
        return

    # 3. 生成每日数据
    logger.info("📈 步骤3: 生成每日毛利率数据")
    daily_data = downloader.generate_daily_data()

    if daily_data is not None:
        elapsed_time = time.time() - start_time
        logger.info(f"✅ 程序执行完成，总耗时: {elapsed_time:.2f} 秒")
        logger.info(f"📁 数据文件保存在: {downloader.output_dir}")
    else:
        logger.error("❌ 程序执行失败")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数波动率因子回测工具 - 近12月年化波动率

功能：
1. 计算恒生指数成分股近12个月年化波动率
2. 波动率因子有效性回测
3. 低波动率异象验证
4. 分组回测分析
5. 可视化结果展示

波动率因子构建方法：
- 使用近12个月（252个交易日）的日收益率数据
- 计算收益率标准差并年化（乘以sqrt(252)）
- 按波动率大小分组：低波动率组合 vs 高波动率组合

理论基础：
- 低波动率异象：低波动率股票往往获得更高的风险调整收益
- 投资者行为偏差：追逐高波动率股票导致其被高估

作者: AI Assistant
创建时间: 2025年1月
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
import os
import time
from typing import Dict, List, Tuple, Optional
import plotly.graph_objects as go
from plotly.subplots import make_subplots

warnings.filterwarnings('ignore')

class HSIVolatilityFactorBacktest:
    """恒生指数波动率因子回测器"""
    
    def __init__(self, price_cache_dir: str = "hsi_price_cache"):
        self.price_cache_dir = price_cache_dir
        self.price_data = {}
        self.volatility_data = {}
        self.backtest_results = {}
        self.lookback_days = 252  # 12个月交易日
        
        # 创建价格缓存目录
        os.makedirs(price_cache_dir, exist_ok=True)
        
    def load_all_price_data(self) -> bool:
        """加载所有股票的价格数据"""
        print(f"📥 加载恒生指数成分股价格数据...")
        
        # 获取缓存目录中的所有价格文件
        cache_files = [f for f in os.listdir(self.price_cache_dir) if f.endswith('_price.csv')]
        
        success_count = 0
        for cache_file in cache_files:
            stock_code = cache_file.replace('_price.csv', '')
            
            try:
                file_path = os.path.join(self.price_cache_dir, cache_file)
                cached_data = pd.read_csv(file_path, parse_dates=['date'])
                
                if not cached_data.empty:
                    cached_data['date'] = pd.to_datetime(cached_data['date'])
                    cached_data.set_index('date', inplace=True)
                    cached_data = cached_data.sort_index()
                    
                    # 计算日收益率
                    cached_data['return'] = cached_data['close'].pct_change()
                    
                    self.price_data[stock_code] = cached_data
                    success_count += 1
                    
            except Exception as e:
                print(f"⚠️  读取 {stock_code} 缓存失败: {e}")
        
        print(f"✅ 价格数据加载完成: {success_count} 只股票")
        return success_count > 0
    
    def calculate_rolling_volatility(self, start_date: str = "2021-01-01", end_date: str = "2024-12-31") -> pd.DataFrame:
        """计算滚动波动率"""
        print(f"\n📊 计算近12月滚动年化波动率...")
        
        start_date = pd.to_datetime(start_date)
        end_date = pd.to_datetime(end_date)
        
        volatility_records = []
        
        # 生成月度计算日期（每月月末）
        calculation_dates = pd.date_range(
            start=start_date, 
            end=end_date, 
            freq='M'  # 月末
        )
        
        print(f"📅 计算时间点: {len(calculation_dates)} 个月末")
        
        for calc_date in calculation_dates:
            print(f"📈 计算 {calc_date.strftime('%Y-%m-%d')} 的波动率...")
            
            date_volatilities = []
            
            for stock_code, price_data in self.price_data.items():
                try:
                    # 获取计算日期前的所有数据
                    period_data = price_data[price_data.index <= calc_date]

                    if len(period_data) < 200:  # 至少需要200个交易日
                        continue

                    # 取最近252个交易日（如果有的话）
                    recent_data = period_data.tail(min(self.lookback_days, len(period_data)))
                    returns = recent_data['return'].dropna()

                    if len(returns) < 100:  # 降低最小要求到100个交易日
                        continue

                    # 计算年化波动率
                    volatility = returns.std() * np.sqrt(252)
                    
                    # 获取股票名称（如果有的话）
                    stock_name = f"Stock_{stock_code}"
                    
                    date_volatilities.append({
                        'date': calc_date,
                        'stock_code': stock_code,
                        'stock_name': stock_name,
                        'volatility_12m': volatility,
                        'return_periods': len(returns),
                        'avg_return': returns.mean(),
                        'current_price': recent_data['close'].iloc[-1] if not recent_data.empty else np.nan
                    })
                    
                except Exception as e:
                    print(f"⚠️  计算 {stock_code} 波动率失败: {e}")
                    continue
            
            volatility_records.extend(date_volatilities)
            print(f"   - 成功计算 {len(date_volatilities)} 只股票的波动率")
        
        # 转换为DataFrame
        volatility_df = pd.DataFrame(volatility_records)
        
        if not volatility_df.empty:
            # 过滤异常值
            volatility_df = volatility_df[
                (volatility_df['volatility_12m'] > 0.05) &  # 最小5%年化波动率
                (volatility_df['volatility_12m'] < 2.0)     # 最大200%年化波动率
            ].copy()
            
            print(f"✅ 波动率计算完成:")
            print(f"   - 总记录数: {len(volatility_df):,}")
            print(f"   - 股票数量: {volatility_df['stock_code'].nunique()}")
            print(f"   - 日期范围: {volatility_df['date'].min().date()} 到 {volatility_df['date'].max().date()}")
            print(f"   - 平均波动率: {volatility_df['volatility_12m'].mean():.2%}")
            print(f"   - 波动率范围: {volatility_df['volatility_12m'].min():.2%} - {volatility_df['volatility_12m'].max():.2%}")
        
        self.volatility_data = volatility_df
        return volatility_df
    
    def generate_rebalance_dates(self, start_date: str, end_date: str) -> pd.DatetimeIndex:
        """生成调仓日期（月度调仓）"""
        start_date = pd.to_datetime(start_date)
        end_date = pd.to_datetime(end_date)
        
        # 月度调仓（每月第一个交易日）
        rebalance_dates = pd.date_range(
            start=start_date, 
            end=end_date, 
            freq='MS'  # 月初
        )
        
        return rebalance_dates
    
    def calculate_volatility_rankings(self, date: pd.Timestamp) -> pd.DataFrame:
        """计算指定日期的波动率排名"""
        # 获取该日期最近的波动率数据
        available_data = self.volatility_data[self.volatility_data['date'] <= date]
        
        if available_data.empty:
            return pd.DataFrame()
        
        # 获取每只股票最新的波动率数据
        latest_data = available_data.groupby('stock_code').last().reset_index()
        
        # 按波动率排序（低到高）
        ranked_data = latest_data.sort_values('volatility_12m', ascending=True).reset_index(drop=True)
        ranked_data['volatility_rank'] = range(1, len(ranked_data) + 1)
        ranked_data['volatility_percentile'] = ranked_data['volatility_rank'] / len(ranked_data)
        
        return ranked_data
    
    def create_volatility_portfolios(self, ranked_data: pd.DataFrame, n_groups: int = 5) -> Dict[str, List[str]]:
        """创建波动率分组投资组合"""
        if ranked_data.empty:
            return {}
        
        portfolios = {}
        group_size = len(ranked_data) // n_groups
        
        for i in range(n_groups):
            start_idx = i * group_size
            if i == n_groups - 1:  # 最后一组包含剩余所有股票
                end_idx = len(ranked_data)
            else:
                end_idx = (i + 1) * group_size
            
            group_stocks = ranked_data.iloc[start_idx:end_idx]['stock_code'].tolist()
            portfolios[f'Group_{i+1}_LowVol'] = group_stocks
        
        return portfolios
    
    def calculate_portfolio_return(self, stocks: List[str], start_date: pd.Timestamp, 
                                 end_date: pd.Timestamp) -> float:
        """计算投资组合收益率"""
        try:
            portfolio_returns = []
            
            for stock_code in stocks:
                if stock_code not in self.price_data:
                    continue
                
                price_data = self.price_data[stock_code]
                
                # 获取期间价格数据
                period_data = price_data[
                    (price_data.index >= start_date) & 
                    (price_data.index <= end_date)
                ]
                
                if len(period_data) < 2:
                    continue
                
                # 计算期间收益率
                start_price = period_data['close'].iloc[0]
                end_price = period_data['close'].iloc[-1]
                
                if start_price > 0:
                    stock_return = (end_price - start_price) / start_price
                    portfolio_returns.append(stock_return)
            
            # 等权重平均
            if portfolio_returns:
                return np.mean(portfolio_returns)
            else:
                return 0.0
                
        except Exception as e:
            print(f"⚠️  计算组合收益失败: {e}")
            return 0.0
    
    def run_volatility_factor_backtest(self, start_date: str = "2021-01-01", end_date: str = "2024-12-31", 
                                     n_groups: int = 5) -> Dict:
        """运行波动率因子回测"""
        print(f"\n🔄 开始波动率因子回测...")
        print(f"📅 回测期间: {start_date} 到 {end_date}")
        
        # 生成调仓日期
        rebalance_dates = self.generate_rebalance_dates(start_date, end_date)
        print(f"📊 调仓次数: {len(rebalance_dates)} 次（月度调仓）")
        
        # 初始化结果存储
        portfolio_returns = {f'Group_{i+1}_LowVol': [] for i in range(n_groups)}
        portfolio_holdings = {f'Group_{i+1}_LowVol': [] for i in range(n_groups)}
        
        # 执行回测
        for i, rebalance_date in enumerate(rebalance_dates[:-1]):
            next_rebalance = rebalance_dates[i + 1]
            
            print(f"\n📊 调仓期间 {i+1}: {rebalance_date.date()} 到 {next_rebalance.date()}")
            
            # 获取波动率排名
            ranked_data = self.calculate_volatility_rankings(rebalance_date)
            
            if ranked_data.empty:
                print(f"⚠️  {rebalance_date.date()} 无可用波动率数据，跳过")
                continue
            
            print(f"   - 可用股票数: {len(ranked_data)}")
            print(f"   - 平均波动率: {ranked_data['volatility_12m'].mean():.2%}")
            print(f"   - 波动率范围: {ranked_data['volatility_12m'].min():.2%} - {ranked_data['volatility_12m'].max():.2%}")
            
            # 创建分组投资组合
            portfolios = self.create_volatility_portfolios(ranked_data, n_groups)
            
            # 计算持有期收益
            for group_name, stocks in portfolios.items():
                if not stocks:
                    continue
                
                # 计算组合收益
                period_return = self.calculate_portfolio_return(stocks, rebalance_date, next_rebalance)
                
                portfolio_returns[group_name].append({
                    'date': rebalance_date,
                    'return': period_return,
                    'stocks_count': len(stocks)
                })
                
                # 计算该组合的平均波动率
                group_volatilities = ranked_data[ranked_data['stock_code'].isin(stocks)]['volatility_12m']
                avg_volatility = group_volatilities.mean() if not group_volatilities.empty else np.nan
                
                portfolio_holdings[group_name].append({
                    'date': rebalance_date,
                    'stocks': stocks,
                    'avg_volatility': avg_volatility,
                    'volatility_range': f"{group_volatilities.min():.1%} - {group_volatilities.max():.1%}" if not group_volatilities.empty else "N/A"
                })
                
                print(f"   - {group_name}: {len(stocks)}只股票, 平均波动率{avg_volatility:.1%}, 收益{period_return:.2%}")
        
        # 转换为DataFrame
        results = {}
        for group_name in portfolio_returns:
            if portfolio_returns[group_name]:
                results[group_name] = pd.DataFrame(portfolio_returns[group_name])
            else:
                results[group_name] = pd.DataFrame()
        
        self.backtest_results = {
            'portfolio_returns': results,
            'portfolio_holdings': portfolio_holdings,
            'rebalance_dates': rebalance_dates
        }
        
        return results

    def calculate_performance_metrics(self) -> pd.DataFrame:
        """计算绩效指标"""
        print(f"\n📈 计算波动率因子绩效指标...")

        if 'portfolio_returns' not in self.backtest_results:
            print("❌ 请先运行回测模拟")
            return pd.DataFrame()

        portfolio_returns = self.backtest_results['portfolio_returns']
        metrics = []

        for group_name, returns_df in portfolio_returns.items():
            if returns_df.empty:
                continue

            returns = returns_df['return'].values

            # 基本统计
            total_return = np.prod(1 + returns) - 1
            periods_per_year = 12  # 月度调仓
            annualized_return = (1 + total_return) ** (periods_per_year / len(returns)) - 1
            volatility = np.std(returns) * np.sqrt(periods_per_year)
            sharpe_ratio = annualized_return / volatility if volatility > 0 else 0

            # 最大回撤
            cumulative_returns = np.cumprod(1 + returns)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = (cumulative_returns - running_max) / running_max
            max_drawdown = np.min(drawdowns)

            # 胜率
            win_rate = np.sum(returns > 0) / len(returns) if len(returns) > 0 else 0

            # 平均持股数
            avg_stocks = returns_df['stocks_count'].mean()

            metrics.append({
                'Portfolio': group_name,
                'Total_Return': total_return,
                'Annualized_Return': annualized_return,
                'Volatility': volatility,
                'Sharpe_Ratio': sharpe_ratio,
                'Max_Drawdown': max_drawdown,
                'Win_Rate': win_rate,
                'Avg_Stocks': avg_stocks,
                'Periods': len(returns)
            })

        metrics_df = pd.DataFrame(metrics)
        self.backtest_results['performance_metrics'] = metrics_df

        return metrics_df

    def analyze_volatility_factor_effectiveness(self) -> Dict:
        """分析波动率因子有效性"""
        print(f"\n🔍 分析波动率因子有效性...")

        if 'performance_metrics' not in self.backtest_results:
            self.calculate_performance_metrics()

        metrics_df = self.backtest_results['performance_metrics']

        if metrics_df.empty:
            return {}

        analysis = {}

        # 分析低波动率 vs 高波动率
        low_vol_group = metrics_df[metrics_df['Portfolio'] == 'Group_1_LowVol']  # 最低波动率
        high_vol_group = metrics_df[metrics_df['Portfolio'] == 'Group_5_LowVol']  # 最高波动率

        if not low_vol_group.empty and not high_vol_group.empty:
            low_return = low_vol_group['Annualized_Return'].iloc[0]
            high_return = high_vol_group['Annualized_Return'].iloc[0]

            low_sharpe = low_vol_group['Sharpe_Ratio'].iloc[0]
            high_sharpe = high_vol_group['Sharpe_Ratio'].iloc[0]

            analysis['volatility_factor_effectiveness'] = {
                'low_vol_return': low_return,
                'high_vol_return': high_return,
                'return_spread': low_return - high_return,
                'low_vol_sharpe': low_sharpe,
                'high_vol_sharpe': high_sharpe,
                'sharpe_spread': low_sharpe - high_sharpe,
                'low_vol_anomaly': low_return > high_return,  # 低波动率异象
                'better_risk_adjusted': low_sharpe > high_sharpe
            }

        # 计算因子单调性
        returns = metrics_df['Annualized_Return'].values
        sharpe_ratios = metrics_df['Sharpe_Ratio'].values

        analysis['return_monotonicity'] = self.calculate_monotonicity(returns, ascending=False)  # 期望递减
        analysis['sharpe_monotonicity'] = self.calculate_monotonicity(sharpe_ratios, ascending=False)  # 期望递减

        # 计算信息系数
        ic_analysis = self.calculate_volatility_information_coefficient()
        analysis['ic_analysis'] = ic_analysis

        self.backtest_results['volatility_factor_analysis'] = analysis
        return analysis

    def calculate_monotonicity(self, values: np.ndarray, ascending: bool = True) -> float:
        """计算单调性"""
        if len(values) < 2:
            return 0.0

        diffs = np.diff(values)
        if len(diffs) == 0:
            return 0.0

        expected_sign = 1 if ascending else -1
        signs = np.sign(diffs)
        monotonicity = np.sum(signs == expected_sign) / len(signs)

        return monotonicity

    def calculate_volatility_information_coefficient(self) -> Dict:
        """计算波动率因子信息系数"""
        try:
            if 'portfolio_returns' not in self.backtest_results:
                return {}

            portfolio_returns = self.backtest_results['portfolio_returns']
            portfolio_holdings = self.backtest_results['portfolio_holdings']

            ic_values = []

            # 对每个调仓期计算IC
            for group_name in portfolio_returns:
                returns_df = portfolio_returns[group_name]
                holdings = portfolio_holdings[group_name]

                for i, (_, row) in enumerate(returns_df.iterrows()):
                    if i < len(holdings):
                        period_return = row['return']
                        avg_volatility = holdings[i]['avg_volatility']
                        # 负相关：波动率越低，期望收益越高
                        ic_values.append({'volatility': -avg_volatility, 'return': period_return})

            if not ic_values:
                return {}

            ic_df = pd.DataFrame(ic_values)

            # 计算相关系数
            correlation = ic_df['volatility'].corr(ic_df['return'])

            return {
                'ic_mean': correlation,
                'ic_std': ic_df['volatility'].std(),
                'ic_ir': correlation / ic_df['volatility'].std() if ic_df['volatility'].std() > 0 else 0,
                'samples': len(ic_values)
            }

        except Exception as e:
            print(f"⚠️  计算IC失败: {e}")
            return {}

    def create_visualization(self, output_file: str = "volatility_factor_backtest_results.html"):
        """创建可视化图表"""
        print(f"\n📊 创建波动率因子可视化...")

        if 'portfolio_returns' not in self.backtest_results:
            print("❌ 请先运行回测模拟")
            return None

        portfolio_returns = self.backtest_results['portfolio_returns']

        # 创建子图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('Cumulative Returns (Volatility Factor)',
                          'Risk-Return Analysis',
                          'Volatility Distribution',
                          'Monthly Returns Distribution'),
            specs=[[{"secondary_y": False}, {"type": "scatter"}],
                   [{"type": "histogram"}, {"type": "box"}]]
        )

        colors = ['#2E8B57', '#FF6347', '#4169E1', '#FFD700', '#8A2BE2']

        # 1. 累积收益曲线
        for i, (group_name, returns_df) in enumerate(portfolio_returns.items()):
            if returns_df.empty:
                continue

            cumulative_returns = np.cumprod(1 + returns_df['return'].values)

            fig.add_trace(
                go.Scatter(
                    x=returns_df['date'],
                    y=cumulative_returns,
                    mode='lines+markers',
                    name=group_name,
                    line=dict(color=colors[i % len(colors)], width=2),
                    marker=dict(size=4),
                    showlegend=True
                ),
                row=1, col=1
            )

        # 2. 风险收益散点图
        if 'performance_metrics' in self.backtest_results:
            metrics_df = self.backtest_results['performance_metrics']

            fig.add_trace(
                go.Scatter(
                    x=metrics_df['Volatility'] * 100,
                    y=metrics_df['Annualized_Return'] * 100,
                    mode='markers+text',
                    text=metrics_df['Portfolio'],
                    textposition='top center',
                    marker=dict(size=12, color=colors[:len(metrics_df)]),
                    name='Risk-Return',
                    showlegend=False
                ),
                row=1, col=2
            )

        # 3. 波动率分布
        if hasattr(self, 'volatility_data') and not self.volatility_data.empty:
            fig.add_trace(
                go.Histogram(
                    x=self.volatility_data['volatility_12m'] * 100,
                    nbinsx=30,
                    name='Volatility Distribution',
                    marker_color='lightblue',
                    showlegend=False
                ),
                row=2, col=1
            )

        # 4. 月度收益分布箱线图
        for i, (group_name, returns_df) in enumerate(portfolio_returns.items()):
            if returns_df.empty:
                continue

            fig.add_trace(
                go.Box(
                    y=returns_df['return'] * 100,
                    name=group_name,
                    marker_color=colors[i % len(colors)],
                    showlegend=False
                ),
                row=2, col=2
            )

        # 更新布局
        fig.update_layout(
            title_text="HSI Volatility Factor Backtest Results",
            showlegend=True,
            height=800,
            width=1400
        )

        # 更新坐标轴标签
        fig.update_xaxes(title_text="Date", row=1, col=1)
        fig.update_yaxes(title_text="Cumulative Return", row=1, col=1)

        fig.update_xaxes(title_text="Volatility (%)", row=1, col=2)
        fig.update_yaxes(title_text="Annual Return (%)", row=1, col=2)

        fig.update_xaxes(title_text="12M Volatility (%)", row=2, col=1)
        fig.update_yaxes(title_text="Frequency", row=2, col=1)

        fig.update_xaxes(title_text="Portfolio Groups", row=2, col=2)
        fig.update_yaxes(title_text="Monthly Return (%)", row=2, col=2)

        # 保存图表
        fig.write_html(output_file)
        print(f"📊 可视化结果已保存到: {output_file}")

        return fig

    def generate_volatility_factor_report(self) -> str:
        """生成波动率因子回测报告"""
        if 'performance_metrics' not in self.backtest_results:
            return "❌ 请先运行回测分析"

        metrics_df = self.backtest_results['performance_metrics']
        factor_analysis = self.backtest_results.get('volatility_factor_analysis', {})

        report = []
        report.append("🎯 恒生指数波动率因子回测报告")
        report.append("=" * 60)
        report.append("")

        # 因子构建说明
        report.append("📊 波动率因子构建方法:")
        report.append("-" * 30)
        report.append("• 计算近12个月（252个交易日）的日收益率标准差")
        report.append("• 年化处理：标准差 × √252")
        report.append("• 按波动率从低到高分组（Group_1为最低波动率组）")
        report.append("• 月度调仓，等权重配置")
        report.append("")

        # 理论基础
        report.append("📚 理论基础 - 低波动率异象:")
        report.append("-" * 30)
        report.append("• 低波动率股票往往获得更高的风险调整收益")
        report.append("• 投资者行为偏差：过度追逐高波动率股票")
        report.append("• 机构投资者约束：杠杆限制导致偏好低波动率")
        report.append("")

        # 绩效指标
        report.append("📈 各组合绩效指标:")
        report.append("-" * 30)
        for _, row in metrics_df.iterrows():
            report.append(f"{row['Portfolio']}:")
            report.append(f"  年化收益率: {row['Annualized_Return']:.2%}")
            report.append(f"  波动率: {row['Volatility']:.2%}")
            report.append(f"  夏普比率: {row['Sharpe_Ratio']:.3f}")
            report.append(f"  最大回撤: {row['Max_Drawdown']:.2%}")
            report.append(f"  胜率: {row['Win_Rate']:.2%}")
            report.append(f"  平均持股数: {row['Avg_Stocks']:.1f}")
            report.append("")

        # 因子有效性分析
        if 'volatility_factor_effectiveness' in factor_analysis:
            effectiveness = factor_analysis['volatility_factor_effectiveness']
            report.append("🔍 波动率因子有效性分析:")
            report.append("-" * 30)
            report.append(f"低波动率组合年化收益: {effectiveness['low_vol_return']:.2%}")
            report.append(f"高波动率组合年化收益: {effectiveness['high_vol_return']:.2%}")
            report.append(f"收益差: {effectiveness['return_spread']:.2%}")
            report.append(f"低波动率组合夏普比率: {effectiveness['low_vol_sharpe']:.3f}")
            report.append(f"高波动率组合夏普比率: {effectiveness['high_vol_sharpe']:.3f}")
            report.append(f"夏普比率差: {effectiveness['sharpe_spread']:.3f}")
            report.append(f"低波动率异象: {'✅ 存在' if effectiveness['low_vol_anomaly'] else '❌ 不存在'}")
            report.append(f"风险调整收益更优: {'✅ 是' if effectiveness['better_risk_adjusted'] else '❌ 否'}")
            report.append("")

        # 单调性分析
        if 'return_monotonicity' in factor_analysis:
            report.append(f"📊 收益率单调性: {factor_analysis['return_monotonicity']:.2%}")
        if 'sharpe_monotonicity' in factor_analysis:
            report.append(f"📊 夏普比率单调性: {factor_analysis['sharpe_monotonicity']:.2%}")
        report.append("")

        # IC分析
        if 'ic_analysis' in factor_analysis:
            ic = factor_analysis['ic_analysis']
            report.append("📊 信息系数(IC)分析:")
            report.append("-" * 30)
            report.append(f"IC均值: {ic.get('ic_mean', 0):.4f}")
            report.append(f"IC标准差: {ic.get('ic_std', 0):.4f}")
            report.append(f"IC信息比率: {ic.get('ic_ir', 0):.4f}")
            report.append(f"样本数: {ic.get('samples', 0)}")
            report.append("")

        # 最佳策略推荐
        best_sharpe = metrics_df.loc[metrics_df['Sharpe_Ratio'].idxmax()]
        best_return = metrics_df.loc[metrics_df['Annualized_Return'].idxmax()]

        report.append("🏆 策略推荐:")
        report.append("-" * 30)
        report.append(f"最佳风险调整收益: {best_sharpe['Portfolio']}")
        report.append(f"  - 年化收益率: {best_sharpe['Annualized_Return']:.2%}")
        report.append(f"  - 夏普比率: {best_sharpe['Sharpe_Ratio']:.3f}")
        report.append(f"  - 最大回撤: {best_sharpe['Max_Drawdown']:.2%}")
        report.append("")
        report.append(f"最高绝对收益: {best_return['Portfolio']}")
        report.append(f"  - 年化收益率: {best_return['Annualized_Return']:.2%}")
        report.append(f"  - 波动率: {best_return['Volatility']:.2%}")
        report.append("")

        # 投资建议
        report.append("💡 投资策略建议:")
        report.append("-" * 30)
        if 'volatility_factor_effectiveness' in factor_analysis:
            effectiveness = factor_analysis['volatility_factor_effectiveness']
            if effectiveness['low_vol_anomaly']:
                report.append("✅ 低波动率异象在恒生指数中存在")
                report.append("📈 建议重点配置低波动率股票组合")
                report.append("🎯 可以通过波动率因子获取超额收益")
            else:
                report.append("❌ 低波动率异象效果不明显")
                report.append("⚠️  建议结合其他因子进行多因子策略")

        report.append("")
        report.append("📋 实施要点:")
        report.append("1. 月度调仓，基于最新12个月波动率数据")
        report.append("2. 等权重配置，控制个股集中度")
        report.append("3. 结合基本面分析，避免价值陷阱")
        report.append("4. 定期监控因子有效性，适时调整策略")

        return "\n".join(report)

    def save_volatility_data(self, output_file: str = "hsi_volatility_factor_data.csv"):
        """保存波动率数据"""
        if hasattr(self, 'volatility_data') and not self.volatility_data.empty:
            self.volatility_data.to_csv(output_file, index=False)
            print(f"📁 波动率数据已保存到: {output_file}")

def main():
    """主函数"""
    start_time = time.time()

    print("🚀 恒生指数波动率因子回测分析")
    print("=" * 50)

    # 创建回测器
    backtest = HSIVolatilityFactorBacktest()

    # 1. 加载价格数据
    if not backtest.load_all_price_data():
        print("❌ 价格数据加载失败，程序退出")
        return

    # 2. 计算滚动波动率
    volatility_df = backtest.calculate_rolling_volatility(
        start_date="2021-01-01",
        end_date="2024-12-31"
    )

    if volatility_df.empty:
        print("❌ 波动率计算失败，程序退出")
        return

    # 3. 运行波动率因子回测
    results = backtest.run_volatility_factor_backtest(
        start_date="2021-01-01",
        end_date="2024-12-31",
        n_groups=5
    )

    # 4. 计算绩效指标
    metrics_df = backtest.calculate_performance_metrics()

    # 5. 分析因子有效性
    factor_analysis = backtest.analyze_volatility_factor_effectiveness()

    # 6. 生成报告
    report = backtest.generate_volatility_factor_report()
    print("\n" + report)

    # 7. 创建可视化
    backtest.create_visualization()

    # 8. 保存结果
    output_dir = "backtest_results"
    os.makedirs(output_dir, exist_ok=True)

    # 保存绩效指标
    metrics_df.to_csv(f"{output_dir}/volatility_factor_performance_metrics.csv", index=False)

    # 保存波动率数据
    backtest.save_volatility_data(f"{output_dir}/hsi_volatility_factor_data.csv")

    # 保存报告
    with open(f"{output_dir}/volatility_factor_backtest_report.txt", 'w', encoding='utf-8') as f:
        f.write(report)

    elapsed_time = time.time() - start_time
    print(f"\n✅ 波动率因子回测分析完成，总耗时: {elapsed_time:.2f} 秒")
    print(f"📁 结果文件保存在: {output_dir}/")

if __name__ == "__main__":
    main()
